@font-face {
    font-family: 'Boldivia';
    src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
    font-style: normal;
    font-weight: normal;
}

#s {
    width: 100%;
    height: 100%;
    z-index: 10000;
    text-align: center;
    margin-left: 23rem;
    margin-top: 0rem;
    color: #785a98;
    font-style: bold;
    font-size: 700px;
    font-family: 'Boldivia';
    /* Make sure it's loaded correctly */
    font-weight: 800;
    overflow-wrap: break-word;
    /* ✅ modern alternative to word-wrap */

}

.design2 {
    height: 300px;
    width: 300px;
    margin-left: -8rem;
    margin-top: -65rem;
    background-color: transparent;
    position: static;
    border: 70px solid #dbdbdb;
    border-radius: 50%;
}

.design3 {
    width: 100px;
    height: 50px;
    color: black;
    font-family: 'boldivia';
    font-size: 2rem;
    position: static;
    margin-top: -16rem;
    margin-left: 2rem;
    cursor: pointer;
}

.design4 {
    width: 100px;
    height: 50px;
    color: black;
    font-style: normal;
    font-family: 'boldivia' <PERSON><PERSON>, Helvetica, sans-serif;
    font-size: 1.2rem;
    position: static;
    margin-left: 15.9rem;
    margin-top: -3rem;
    cursor: pointer;
}

hr {
    border: none;
    height: 2px;
    background-color: black;
    width: 100%;
    margin: 4px 0;
}

#hr-1 {
    width: 100px;
    height: 1.5px;
    position: static;
    margin-inline-start: 4rem;
    margin-top: -1rem;
}

#hr-2 {
    width: 100px;
    height: 1.5px;
    position: static;
    background-color: rgb(185, 185, 185);
    left: 58.85rem;
    bottom: 30.65rem;
}

#hr-3 {
    width: 100px;
    position: static;
    left: 30.5rem;
    bottom: 11.6rem;
}

#hr-4 {
    width: 100px;
    background-color: rgb(185, 185, 185);
    position: static;
    left: 58.95rem;
    bottom: 11.6rem;
    ;
}

.design-word{
    display: flex;
    height: 400px;
    width: 400px;
    background-color: aqua;
    z-index: 1000;
}

.design5 {
    height: auto;
    width: fit-content;
    color: black;
    font-style: normal;
    font-size: 1.2rem;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    position: static;
    margin-left: 27.5rem;
    margin-top: 5rem;
}

.design6 {
    height: auto;
    width: 0px;
    z-index: 10000;
    color: rgb(185, 185, 185);
    font-style: normal;
    font-size: 1.2rem;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    position: relative;
    top: 11.5rem;
}

.design7 {
    height: auto;
    width: fit-content;
    color: black;
    font-style: normal;
    font-size: 1.2rem;
    font-family: Arial, Helvetica, sans-serif;
    position: static;
    left: 26.9rem;
    top: 30.5rem;
}

.design8 {
    height: auto;
    width: fit-content;
    color: rgb(185, 185, 185);
    font-style: normal;
    font-size: 1.2rem;
    font-family: Arial, Helvetica, sans-serif;
    position: static;
    left: 65.7rem;
    top: 30.5rem;
}

.design9 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: static;
    left: 20rem;
    top: 16.5rem;

}

.design10 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: static;
    left: 25.5rem;
    top: 16.5rem;

}

.design11 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: static;
    left: 33.5rem;
    top: 16.5rem;

}

.design12 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: static;
    left: 59rem;
    top: 16.5rem;

}

.design13 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: static;
    left: 67rem;
    top: 16.5rem;

}

.design14 {
    height: 100px;
    width: 100px;
    color: rgb(235, 235, 235);
    font-style: normal;
    font-size: 10rem;
    font-family: 'boldivia';
    position: static;
    left: 74rem;
    top: 16.5rem;

}

.design15 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1rem;
    position: static;
    left: 65rem;
    top: 3rem;
    cursor: pointer;
}

.design16 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1rem;
    position: static;
    left: 71.5rem;
    top: 3rem;
    cursor: pointer;
}

.design17 {
    width: 100px;
    height: 50px;
    color: rgb(190, 190, 190);
    font-style: normal;
    font-family: 'boldivia' Arial, Helvetica, sans-serif;
    font-size: 1rem;
    position: static;
    left: 85rem;
    top: 3rem;
    cursor: pointer;

}

.blinking-down-arrow {
  animation: bounce 1.5s infinite;
  position: static;
  top:40rem;
  left: 46.56rem;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(10px); }
}
